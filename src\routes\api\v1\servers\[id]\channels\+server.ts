import type { <PERSON>quest<PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import { error, success } from "$lib/utils"

export const GET: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const server = await db.query.servers.findFirst({
		where: eq(schema.servers.id, params.id),
	})

	if (!server) return error(404, "Server not found")

	const channels = await db.query.channels.findMany({
		where: eq(schema.channels.serverId, params.id),
	})

	return success(channels)
}
