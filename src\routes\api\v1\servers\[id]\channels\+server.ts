import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq, and } from "drizzle-orm"
import { error, success } from "$lib/utils"
import * as z from "zod"
import { encodeBase32LowerCase } from "@oslojs/encoding"

export const GET: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const server = await db.query.servers.findFirst({
		where: eq(schema.servers.id, params.id),
	})

	if (!server) return error(404, "Server not found")

	const channels = await db.query.channels.findMany({
		where: eq(schema.channels.serverId, params.id),
	})

	return success(channels)
}

export const POST: RequestHandler = async ({ params, request, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const bodySchema = z.object({
		name: z.string().min(1).max(100),
		description: z.string().min(1).max(1000),
	})

	let json
	try {
		json = await request.json()
	} catch (e) {
		return error(400, "Invalid JSON")
	}

	const body = bodySchema.safeParse(json)
	if (!body.success) return error(400, body.error.message)

	// Check if user is a member of the server
	const userInServer = await db.query.userInServer.findFirst({
		where: and(
			eq(schema.userInServer.userId, locals.user.id),
			eq(schema.userInServer.serverId, params.id)
		),
	})

	if (!userInServer) return error(403, "Not a member of this server")

	// Check if server exists
	const server = await db.query.servers.findFirst({
		where: eq(schema.servers.id, params.id),
	})

	if (!server) return error(404, "Server not found")

	// Only server owner can create channels
	if (server.ownerId !== locals.user.id) {
		return error(403, "Only the server owner can create channels")
	}

	// Generate channel ID
	const bytes = crypto.getRandomValues(new Uint8Array(15))
	const channelId = encodeBase32LowerCase(bytes)

	try {
		// Create the channel
		await db.insert(schema.channels).values({
			id: channelId,
			serverId: params.id,
			name: body.data.name,
			description: body.data.description,
			createdAt: new Date(),
		})

		return success({
			id: channelId,
			name: body.data.name,
			description: body.data.description,
			serverId: params.id,
			createdAt: new Date(),
		})
	} catch (e) {
		console.error("Error creating channel:", e)
		return error(500, "Failed to create channel")
	}
}
