{"name": "deezcord3", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "bits-ui": "^2.8.11", "clsx": "^2.1.1", "drizzle-kit": "^0.30.2", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "5.33.14", "svelte-check": "^4.0.0", "tailwind-merge": "^3.0.2", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.3", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"@libsql/client": "^0.14.0", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "drizzle-orm": "^0.40.0", "mode-watcher": "0.5.1", "zod": "^4.0.5"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}