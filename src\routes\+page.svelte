<script lang="ts">
	import { But<PERSON> } from "$lib/components/ui/button"
	import { MessageCircle, Users, Hash, Zap } from "@lucide/svelte"
</script>

<svelte:head>
	<title>Wyvern</title>
</svelte:head>

<div class="from-background to-secondary/20 min-h-screen bg-gradient-to-br">
	<header class="bg-secondary/50 border-b backdrop-blur-sm">
		<div class="container mx-auto px-4 py-4">
			<div class="flex items-center justify-center space-x-2">
				<h1 class="text-2xl font-bold">Wyvern</h1>
			</div>
		</div>
	</header>

	<!-- Hero Section -->
	<main class="container mx-auto px-4 py-16">
		<div class="mx-auto max-w-4xl text-center">
			<div class="flex flex-col items-center justify-center gap-4 sm:flex-row">
				<Button size="lg" href="/dashboard" class="px-8 py-3 text-lg">Entrar</Button>
			</div>
		</div>
	</main>
</div>
