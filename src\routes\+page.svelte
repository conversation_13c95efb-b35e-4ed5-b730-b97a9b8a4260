<script lang="ts">
	import { <PERSON><PERSON> } from "$lib/components/ui/button"
	import { MessageCircle, Users, Hash, Zap } from "@lucide/svelte"
</script>

<svelte:head>
	<title>Wyvern</title>
</svelte:head>

<div class="from-background to-secondary/20 min-h-screen bg-gradient-to-br">
	<header class="bg-secondary/50 border-b backdrop-blur-sm">
		<div class="container mx-auto px-4 py-4">
			<div class="flex items-center space-x-2 justify-center">
				<h1 class="text-2xl font-bold">Wyvern</h1>
			</div>
		</div>
	</header>

	<!-- Hero Section -->
	<main class="container mx-auto px-4 py-16">
		<div class="mx-auto max-w-4xl text-center">
			<h2
				class="from-primary to-primary/60 mb-6 bg-gradient-to-r bg-clip-text text-5xl font-bold text-transparent md:text-6xl"
			>
				Comunicação Simples e Poderosa
			</h2>
			<p class="text-muted-foreground mx-auto mb-8 max-w-2xl text-xl">
				Conecte-se com a sua comunidade, crie servidores personalizados e mantenha conversas
				organizadas em canais dedicados.
			</p>
			<div class="flex flex-col items-center justify-center gap-4 sm:flex-row">
				<Button size="lg" href="/dashboard" class="px-8 py-3 text-lg">Entrar</Button>
			</div>
		</div>
	</main>
</div>
