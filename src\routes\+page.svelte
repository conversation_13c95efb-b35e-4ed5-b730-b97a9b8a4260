<script lang="ts">
	import { But<PERSON> } from "$lib/components/ui/button"
	import Header from "$lib/components/ui/header.svelte"
</script>

<svelte:head>
	<title>Wyvern</title>
</svelte:head>

<div class="from-background to-secondary/20 min-h-screen bg-gradient-to-br">
	<Header />

	<!-- Hero Section -->
	<main class="container mx-auto px-4 py-16">
		<div class="mx-auto max-w-4xl text-center">
			<div class="flex flex-col items-center justify-center gap-4 sm:flex-row">
				<Button size="lg" href="/dashboard" class="px-8 py-3 text-lg">Entrar</Button>
			</div>
		</div>
	</main>
</div>
