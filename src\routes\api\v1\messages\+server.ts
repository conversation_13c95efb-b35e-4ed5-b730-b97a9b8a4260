import type { <PERSON>quest<PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import { error, success } from "$lib/utils"
import * as z from "zod"

export const POST: RequestHandler = async ({ params, request, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	/* TODO */

	error(500, "TODO")

	const bodySchema = z.object({
		name: z.string().min(1).max(100).optional(),
		description: z.string().min(1).max(1000).optional(),
	})

	let json

	try {
		json = await request.json()
	} catch (e) {
		return error(400, "Invalid JSON")
	}

	const body = bodySchema.safeParse(json)
	if (!body.success) return error(400, body.error.message)

	return success()
}
