import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import * as z from "zod"
import { getAuthorizedChannel } from "$lib/server/auth"
import { error, success } from "$lib/utils"

export const GET: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const channel = await getAuthorizedChannel(params.id, locals.user.id)

	return success(channel)
}

export const PATCH: RequestHandler = async ({ params, locals, request }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const bodySchema = z.object({
		name: z.string().min(1).max(100).optional(),
		description: z.string().min(1).max(1000).optional(),
	})

	let json

	try {
		json = await request.json()
	} catch (e) {
		return error(400, "Invalid JSON")
	}

	const body = bodySchema.safeParse(json)
	if (!body.success) return error(400, body.error.message)

	const channel = await getAuthorizedChannel(params.id, locals.user.id, true)

	try {
		await db
			.update(schema.channels)
			.set({ name: body.data.name, description: body.data.description })
			.where(eq(schema.channels.id, channel.id))

		return success({ id: channel.id, name: body.data.name, description: body.data.description })
	} catch (e) {
		console.error("Error updating channel:", e)
		return error(500, "Failed to update channel")
	}
}

export const DELETE: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const channel = await getAuthorizedChannel(params.id, locals.user.id, true)

	try {
		// Delete all messages in this channel first
		await db.delete(schema.messages).where(eq(schema.messages.channelId, channel.id))

		// Then delete the channel
		await db.delete(schema.channels).where(eq(schema.channels.id, channel.id))

		return success({ message: "Channel deleted successfully" })
	} catch (e) {
		console.error("Error deleting channel:", e)
		return error(500, "Failed to delete channel")
	}
}
