<script lang="ts">
	import { enhance } from "$app/forms"
	import type { ActionData } from "./$types"

	let { form }: { form: ActionData } = $props()
</script>

<h1>Iniciar Sessão/Registar</h1>
<form method="post" action="?/login" use:enhance>
	<label>
		Nome de utilizador
		<input name="username" />
	</label>
	<label>
		Palavra-passe
		<input type="password" name="password" />
	</label>
	<button>Iniciar <PERSON><PERSON><PERSON></button>
	<button formaction="?/register">Registar</button>
</form>
<p style="color: red">{form?.message ?? ""}</p>
