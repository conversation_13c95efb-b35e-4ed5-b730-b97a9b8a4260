<script lang="ts">
	import { enhance } from "$app/forms"
	import type { ActionData } from "./$types"
	import Header from "$lib/components/ui/header.svelte"

	let { form }: { form: ActionData } = $props()
</script>

<svelte:head>
	<title>Wyvern - Login</title>
</svelte:head>

<div class="from-background to-secondary/20 min-h-screen bg-gradient-to-br">
	<Header showAuth={false} />

	<div class="bg-card container mx-auto mt-16 max-w-md rounded-lg border p-6 shadow-sm">
		<h1 class="mb-6 text-center text-2xl font-bold">Iniciar Sessão/Registar</h1>
		<form method="post" action="?/login" use:enhance class="space-y-4">
			<div class="space-y-2">
				<label class="text-sm font-medium">
					Nome de utilizador
					<input name="username" class="mt-1 w-full rounded-md border p-2" />
				</label>
			</div>
			<div class="space-y-2">
				<label class="text-sm font-medium">
					Palavra-passe
					<input type="password" name="password" class="mt-1 w-full rounded-md border p-2" />
				</label>
			</div>
			<div class="flex space-x-2 pt-2">
				<button
					class="bg-primary text-primary-foreground hover:bg-primary/90 flex-1 rounded-md px-4 py-2"
				>
					Iniciar Sessão
				</button>
				<button
					formaction="?/register"
					class="bg-secondary hover:bg-secondary/80 flex-1 rounded-md px-4 py-2"
				>
					Registar
				</button>
			</div>
		</form>
		{#if form?.message}
			<p class="text-destructive mt-4 text-center text-sm">{form.message}</p>
		{/if}
	</div>
</div>
