import { relations } from "drizzle-orm"
import { sqliteTable, text, integer, primaryKey } from "drizzle-orm/sqlite-core"

export const users = sqliteTable("users", {
	id: text("id").primaryKey(),
	pfp: text("pfp"),
	username: text("username").notNull().unique(),
	passwordHash: text("password_hash").notNull(),
})

export const userRelations = relations(users, ({ one, many }) => ({
	sessions: many(sessions),
	serversOwned: many(servers),
	userInServers: many(userInServer),
	messages: many(messages),
}))

export const sessions = sqliteTable("sessions", {
	id: text("id").primaryKey(),
	userId: text("user_id")
		.notNull()
		.references(() => users.id),
	expiresAt: integer("expires_at", { mode: "timestamp" }).notNull(),
})

export const sessionRelations = relations(sessions, ({ one, many }) => ({
	owner: one(users, {
		fields: [sessions.userId],
		references: [users.id],
	}),
}))

export const servers = sqliteTable("servers", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	description: text("description").notNull(),
	image: text("image"),
	ownerId: text("owner_id")
		.notNull()
		.references(() => users.id),
	createdAt: integer("created_at", { mode: "timestamp" }).notNull(),
})

export const serverRelations = relations(servers, ({ one, many }) => ({
	owner: one(users, {
		fields: [servers.ownerId],
		references: [users.id],
	}),
	userInServers: many(userInServer),
	channels: many(channels),
}))

export const channels = sqliteTable("channels", {
	id: text("id").primaryKey(),
	serverId: text("server_id")
		.notNull()
		.references(() => servers.id),
	name: text("name").notNull(),
	description: text("description").notNull(),
	createdAt: integer("created_at", { mode: "timestamp" }).notNull(),
})

export const channelRelations = relations(channels, ({ one, many }) => ({
	server: one(servers, {
		fields: [channels.serverId],
		references: [servers.id],
	}),
	messages: many(messages),
}))

export const messages = sqliteTable("messages", {
	id: text("id").primaryKey(),
	authorId: text("author_id")
		.notNull()
		.references(() => users.id),
	channelId: text("channel_id")
		.notNull()
		.references(() => channels.id),
	content: text("content").notNull(),
	createdAt: integer("created_at", { mode: "timestamp" }).notNull(),
})

export const messageRelations = relations(messages, ({ one, many }) => ({
	author: one(users, {
		fields: [messages.authorId],
		references: [users.id],
	}),
	channel: one(channels, {
		fields: [messages.channelId],
		references: [channels.id],
	}),
}))

export const userInServer = sqliteTable(
	"user_in_server",
	{
		userId: text("user_id")
			.notNull()
			.references(() => users.id),
		serverId: text("server_id")
			.notNull()
			.references(() => servers.id),
	},
	(t) => [primaryKey({ columns: [t.userId, t.serverId] })]
)

export const userInServerRelations = relations(userInServer, ({ one, many }) => ({
	user: one(users, {
		fields: [userInServer.userId],
		references: [users.id],
	}),
	server: one(servers, {
		fields: [userInServer.serverId],
		references: [servers.id],
	}),
}))

export type Session = typeof sessions.$inferSelect

export type User = typeof users.$inferSelect