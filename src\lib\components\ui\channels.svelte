<script lang="ts">
	import { cn } from "$lib/utils"
	import { Plus, Edit, Trash2 } from "@lucide/svelte"
	import * as Dialog from "$lib/components/ui/dialog"
	import * as AlertDialog from "$lib/components/ui/alert-dialog"
	import * as Tooltip from "$lib/components/ui/tooltip"
	import { Input } from "$lib/components/ui/input"
	import { Label } from "$lib/components/ui/label"
	import { Button } from "$lib/components/ui/button"

	export type ChannelType = {
		id: string
		name: string
		serverId: string
		description?: string
		createdAt?: Date
	}

	type Props = {
		channels: ChannelType[]
		onSelect: (channelId: ChannelType["id"] | null) => void
		serverId: string | null
		onChannelAdded?: (channel: ChannelType) => void
		onChannelDeleted?: (channelId: string) => void
		onChannelUpdated?: (channel: ChannelType) => void
		currentUserId?: string
	}

	const {
		channels,
		onSelect,
		serverId,
		onChannelAdded,
		onChannelDeleted,
		onChannelUpdated,
		currentUserId,
	}: Props = $props()
	let selected: ChannelType["id"] | null = $state(null)

	// Form states
	let createChannelName = $state("geral")
	let createChannelDescription = $state("Discussão geral")
	let isCreating = $state(false)
	let dialogOpen = $state(false)

	// Edit channel states
	let editChannelName = $state("")
	let editChannelDescription = $state("")
	let editingChannelId = $state<string | null>(null)
	let editDialogOpen = $state(false)
	let isUpdating = $state(false)
	let isDeleting = $state(false)
	let deleteAlertOpen = $state(false)

	$effect(() => {
		onSelect(selected)
	})

	async function createChannel() {
		if (isCreating || !serverId) return
		isCreating = true

		try {
			const response = await fetch(`/api/v1/servers/${serverId}/channels`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					name: createChannelName,
					description: createChannelDescription,
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Add the new channel to the list
			if (onChannelAdded) {
				onChannelAdded({
					id: result.result.id,
					name: result.result.name,
					serverId: result.result.serverId,
					description: result.result.description,
					createdAt: new Date(result.result.createdAt),
				})
			}

			// Reset form and close dialog
			createChannelName = "geral"
			createChannelDescription = "Discussão geral"
			dialogOpen = false
		} catch (error) {
			alert("Falha ao criar canal")
		} finally {
			isCreating = false
		}
	}

	function handleChannelContextMenu(channelId: string, event: MouseEvent) {
		event.preventDefault()
		const channel = channels.find((c) => c.id === channelId)
		if (!channel) return

		// For now, allow all server members to edit channels
		// You could add server owner check here if needed
		editingChannelId = channelId
		editChannelName = channel.name
		editChannelDescription = channel.description || ""
		editDialogOpen = true
	}

	async function updateChannel() {
		if (!editingChannelId || isUpdating) return
		isUpdating = true

		try {
			const response = await fetch(`/api/v1/channels/${editingChannelId}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					name: editChannelName,
					description: editChannelDescription,
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Update the channel in the list
			if (onChannelUpdated) {
				onChannelUpdated({
					...channels.find((c) => c.id === editingChannelId)!,
					name: editChannelName,
					description: editChannelDescription,
				})
			}

			// Close dialog
			editDialogOpen = false
			editingChannelId = null
		} catch (error) {
			alert("Falha ao atualizar canal")
		} finally {
			isUpdating = false
		}
	}

	function showDeleteConfirmation() {
		deleteAlertOpen = true
	}

	async function confirmDeleteChannel() {
		if (!editingChannelId || isDeleting) return

		isDeleting = true

		try {
			const response = await fetch(`/api/v1/channels/${editingChannelId}`, {
				method: "DELETE",
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Remove channel from list
			if (onChannelDeleted) {
				onChannelDeleted(editingChannelId)
			}

			// Close dialogs
			editDialogOpen = false
			deleteAlertOpen = false
			editingChannelId = null
		} catch (error) {
			alert("Falha ao eliminar canal")
		} finally {
			isDeleting = false
		}
	}
</script>

<div class="mr-4 ml-4 flex flex-col gap-2">
	<div class="mb-2 flex items-center justify-between">
		<h3 class="text-muted-foreground text-sm font-semibold">CANAIS</h3>
		{#if serverId}
			<Dialog.Root bind:open={dialogOpen}>
				<Dialog.Trigger class="bg-secondary hover:bg-secondary/80 rounded-full p-1">
					<Plus size={16} />
				</Dialog.Trigger>
				<Dialog.Content class="max-w-md">
					<Dialog.Title>Criar Canal</Dialog.Title>
					<div class="grid gap-4 py-4">
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="channel-name" class="text-right">Nome</Label>
							<Input
								id="channel-name"
								bind:value={createChannelName}
								class="col-span-3"
								placeholder="Nome do canal"
							/>
						</div>
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="channel-description" class="text-right">Descrição</Label>
							<Input
								id="channel-description"
								bind:value={createChannelDescription}
								class="col-span-3"
								placeholder="Descrição do canal"
							/>
						</div>
					</div>
					<Dialog.Footer>
						<Button onclick={createChannel} disabled={isCreating || !createChannelName.trim()}>
							{isCreating ? "A criar..." : "Criar"}
						</Button>
					</Dialog.Footer>
				</Dialog.Content>
			</Dialog.Root>

			<!-- Edit Channel Dialog -->
			<Dialog.Root bind:open={editDialogOpen}>
				<Dialog.Content class="max-w-md">
					<Dialog.Title>Editar Canal</Dialog.Title>
					<div class="grid gap-4 py-4">
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="edit-channel-name" class="text-right">Nome</Label>
							<Input
								id="edit-channel-name"
								bind:value={editChannelName}
								class="col-span-3"
								placeholder="Nome do canal"
							/>
						</div>
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="edit-channel-description" class="text-right">Descrição</Label>
							<Input
								id="edit-channel-description"
								bind:value={editChannelDescription}
								class="col-span-3"
								placeholder="Descrição do canal"
							/>
						</div>
					</div>
					<Dialog.Footer class="flex justify-between">
						<Button
							variant="destructive"
							onclick={showDeleteConfirmation}
							disabled={isDeleting || isUpdating}
						>
							<Trash2 class="mr-2 h-4 w-4" />
							Eliminar
						</Button>
						<Button
							onclick={updateChannel}
							disabled={isUpdating || isDeleting || !editChannelName.trim()}
						>
							<Edit class="mr-2 h-4 w-4" />
							{isUpdating ? "A guardar..." : "Guardar"}
						</Button>
					</Dialog.Footer>
				</Dialog.Content>
			</Dialog.Root>

			<!-- Delete Confirmation Alert Dialog -->
			<AlertDialog.Root bind:open={deleteAlertOpen}>
				<AlertDialog.Content>
					<AlertDialog.Header>
						<AlertDialog.Title>Eliminar Canal</AlertDialog.Title>
						<AlertDialog.Description>
							Tem a certeza que quer eliminar este canal? Esta ação não pode ser desfeita. Todas as
							mensagens serão permanentemente eliminadas.
						</AlertDialog.Description>
					</AlertDialog.Header>
					<AlertDialog.Footer>
						<AlertDialog.Cancel>Cancelar</AlertDialog.Cancel>
						<AlertDialog.Action onclick={confirmDeleteChannel} disabled={isDeleting}>
							{isDeleting ? "A eliminar..." : "Eliminar"}
						</AlertDialog.Action>
					</AlertDialog.Footer>
				</AlertDialog.Content>
			</AlertDialog.Root>
		{/if}
	</div>

	{#each channels as channel}
		<Tooltip.Provider delayDuration={0}>
			<Tooltip.Root>
				<Tooltip.Trigger>
					<div
						class={cn(
							"hover:bg-secondary/50 w-full cursor-pointer rounded p-1 pr-2 pl-2 text-left",
							selected === channel.id && "bg-foreground/10"
						)}
						onclick={() => (selected = channel.id)}
						oncontextmenu={(e) => handleChannelContextMenu(channel.id, e)}
						role="none"
					>
						# {channel.name}
					</div>
				</Tooltip.Trigger>
				<Tooltip.Content side="right">
					<div class="text-lg font-bold">#{channel.name}</div>
					{#if channel.description}
						<div>{channel.description}</div>
					{/if}
					<div class="text-muted-foreground text-sm">ID do Canal: {channel.id}</div>
				</Tooltip.Content>
			</Tooltip.Root>
		</Tooltip.Provider>
	{/each}

	{#if channels.length === 0 && serverId}
		<div class="text-muted-foreground text-sm italic">
			Ainda não há canais. Crie um para começar!
		</div>
	{/if}
</div>
