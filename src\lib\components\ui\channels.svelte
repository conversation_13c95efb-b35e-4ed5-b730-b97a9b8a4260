<script lang="ts">
	import { cn } from "$lib/utils"

	export type ChannelType = { id: string; name: string; serverId: string }

	type Props = {
		channels: ChannelType[]
		onSelect: (channelId: s | null) => void
	}

	const { channels, onSelect }: Props = $props()
	let selected: ChannelType["id"] | null = $state(null)

	$effect(() => {
		onSelect(selected)
	})
</script>

<div class="mr-4 ml-4 flex flex-col gap-2">
	{#each channels as channel}
		<div
			class={cn("", selected === channel.id && "bg-foreground/10")}
			onclick={() => (selected = channel.id)}
			role="none"
		>
			{channel.name}
		</div>
	{/each}
</div>
