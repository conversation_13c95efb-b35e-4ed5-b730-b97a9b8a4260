import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs))
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChild<T> = T extends { child?: any } ? Omit<T, "child"> : T
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChildren<T> = T extends { children?: any } ? Omit<T, "children"> : T
export type WithoutChildrenOrChild<T> = WithoutChildren<WithoutChild<T>>
export type WithElementRef<T, U extends HTMLElement = HTMLElement> = T & { ref?: U | null }

export function formatDate(date: Date): string {
    const now = new Date()

	const sameDay = now.toDateString() === date.toDateString()

	const yesterday = new Date(now)
	yesterday.setDate(now.getDate() - 1)
	const isYesterday = yesterday.toDateString() === date.toDateString()

	if (sameDay) {
		return date.toLocaleTimeString([], { hour: "numeric", minute: "2-digit" })
	} else if (isYesterday) {
		return `Yesterday at ${date.toLocaleTimeString([], { hour: "numeric", minute: "2-digit" })}`
	} else {
		return date.toLocaleString([], {
			year: "2-digit",
			month: "numeric",
			day: "numeric",
			hour: "numeric",
			minute: "2-digit",
		})
	}
}

export function clamp(value: number, min: number, max: number): number {
	return Math.min(Math.max(value, min), max)
}

export function success(result: string | Object | undefined = undefined, status = 200) {
	return new Response(JSON.stringify({ success: true, result }), {
		status,
		headers: {
			"Content-Type": "application/json",
		},
	})
}

export function error(status: number, message: string) {
	return new Response(JSON.stringify({ success: false, error: message }), {
		status,
		headers: {
			"Content-Type": "application/json",
		},
	})
}
