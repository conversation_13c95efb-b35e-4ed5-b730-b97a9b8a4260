<script lang="ts">
	import { formatDate } from "$lib/utils"
	import { onMount } from "svelte"

	let {
		date,
		class: className,
	}: {
		date: Date
		class?: string
	} = $props()

	let formatted = $state(formatDate(date))
	onMount(() => {
		const interval = setInterval(() => {
			formatted = formatDate(date)
		}, 1000)

		return () => {
			clearInterval(interval)
		}
	})
</script>

<div class={className}>{formatted}</div>
