<script lang="ts">
	import Server from "$lib/components/ui/server.svelte"
	import type { ComponentProps } from "svelte"
	import { Plus } from "@lucide/svelte"
	import * as Tooltip from "$lib/components/ui/tooltip"
	import * as Dialog from "$lib/components/ui/dialog"

	import { Input } from "$lib/components/ui/input"
	import { Label } from "$lib/components/ui/label"
	import { Button } from "$lib/components/ui/button"

	type ServerType = ComponentProps<typeof Server>["server"]

	type Props = {
		servers: ServerType[]
		onSelect: (serverId: ServerType["id"] | null) => void
		onServerAdded?: (server: ServerType) => void
	}

	const { servers, onSelect, onServerAdded }: Props = $props()
	let selected: ServerType["id"] | null = $state(null)

	// Form states
	let createServerName = $state("Meu servidor")
	let createServerDescription = $state("Descrição")
	let joinServerId = $state("")
	let isCreating = $state(false)
	let isJoining = $state(false)
	let dialogOpen = $state(false)

	$effect(() => {
		onSelect(selected)
	})

	async function createServer() {
		if (isCreating) return
		isCreating = true

		try {
			const response = await fetch("/api/v1/servers", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					name: createServerName,
					description: createServerDescription,
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Add the new server to the list
			if (onServerAdded) {
				onServerAdded({
					id: result.result.id,
					name: result.result.name,
					description: result.result.description,
					image: "",
					createdAt: new Date(),
				})
			}

			// Reset form and close dialog
			createServerName = "Meu servidor"
			createServerDescription = "Descrição"
			dialogOpen = false
		} catch (error) {
			alert("Failed to create server")
		} finally {
			isCreating = false
		}
	}

	async function joinServer() {
		if (isJoining) return
		isJoining = true

		try {
			const response = await fetch("/api/v1/servers/join", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					serverId: joinServerId,
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Add the joined server to the list
			if (onServerAdded) {
				onServerAdded({
					id: result.result.id,
					name: result.result.name,
					description: result.result.description,
					image: result.result.image || "",
					createdAt: new Date(result.result.createdAt),
				})
			}

			// Reset form and close dialog
			joinServerId = ""
			dialogOpen = false
		} catch (error) {
			alert("Failed to join server")
		} finally {
			isJoining = false
		}
	}
</script>

<div class="mr-4 ml-4 flex flex-col gap-2">
	{#each servers as server}
		<div onclick={() => (selected = server.id)} role="none">
			<Server {server} selected={selected === server.id} />
		</div>
	{/each}

	<Dialog.Root bind:open={dialogOpen}>
		<Dialog.Trigger class="bg-secondary rounded-full p-2">
			<Plus />
		</Dialog.Trigger>
		<Dialog.Content class="max-w-md">
			<div class="space-y-6">
				<!-- Create Server Section -->
				<div>
					<Dialog.Title>Criar um servidor</Dialog.Title>
					<div class="grid gap-4 py-4">
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="create-name" class="text-right">Nome</Label>
							<Input
								id="create-name"
								bind:value={createServerName}
								class="col-span-3"
								placeholder="Nome do servidor"
							/>
						</div>
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="create-description" class="text-right">Descrição</Label>
							<Input
								id="create-description"
								bind:value={createServerDescription}
								class="col-span-3"
								placeholder="Descrição do servidor"
							/>
						</div>
					</div>
					<div class="flex justify-end">
						<Button onclick={createServer} disabled={isCreating}>
							{isCreating ? "Criando..." : "Criar"}
						</Button>
					</div>
				</div>

				<!-- Divider -->
				<div class="border-border border-t"></div>

				<!-- Join Server Section -->
				<div>
					<Dialog.Title>Entrar num servidor</Dialog.Title>
					<div class="grid gap-4 py-4">
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="join-id" class="text-right">ID</Label>
							<Input
								id="join-id"
								bind:value={joinServerId}
								class="col-span-3"
								placeholder="ID do servidor"
							/>
						</div>
					</div>
					<div class="flex justify-end">
						<Button onclick={joinServer} disabled={isJoining || !joinServerId.trim()}>
							{isJoining ? "Entrando..." : "Entrar"}
						</Button>
					</div>
				</div>
			</div>
		</Dialog.Content>
	</Dialog.Root>
</div>
