<script lang="ts">
	import Server from "$lib/components/ui/server.svelte"
	import type { ComponentProps } from "svelte"
	import { Plus } from "@lucide/svelte"
	import * as Tooltip from "$lib/components/ui/tooltip"
	import * as Dialog from "$lib/components/ui/dialog"
	import { Input } from "$lib/components/ui/input"
	import { Label } from "$lib/components/ui/label"
	import { Button } from "$lib/components/ui/button"

	type ServerType = ComponentProps<typeof Server>["server"]

	type Props = {
		servers: ServerType[]
		onSelect: (serverId: ServerType["id"] | null) => void
	}

	const { servers, onSelect }: Props = $props()
	let selected: ServerType["id"] | null = $state(null)

	$effect(() => {
		onSelect(selected)
	})
</script>

<div class="mr-4 ml-4 flex flex-col gap-2">
	{#each servers as server}
		<div onclick={() => (selected = server.id)} role="none">
			<Server {server} selected={selected === server.id} />
		</div>
	{/each}

	<Dialog.Root>
		<Dialog.Trigger class="bg-secondary rounded-full p-2">
			<Plus />
		</Dialog.Trigger>
		<Dialog.Content>
			<Dialog.Title>Criar um servidor</Dialog.Title>
			<div class="grid gap-4 py-4">
				<div class="grid grid-cols-4 items-center gap-4">
					<Label for="name" class="text-right">Nome</Label>
					<Input id="name" value="Meu servidor" class="col-span-3" />
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label for="description" class="text-right">Descrição</Label>
					<Input id="description" value="Descrição" class="col-span-3" />
				</div>
			</div>
			<Dialog.Footer>
				<Button>Criar</Button>
			</Dialog.Footer>

			<Dialog.Title>Entrar num servidor</Dialog.Title>
			<div class="grid gap-4 py-4">
				<div class="grid grid-cols-4 items-center gap-4">
					<Label for="id" class="text-right">Id</Label>
					<Input id="id" value="5" class="col-span-3" />
				</div>
			</div>
			<Dialog.Footer>
				<Button>Entrar</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
</div>
