<script lang="ts">
	import Server from "$lib/components/ui/server.svelte"
	import type { ComponentProps } from "svelte"
	import { Plus, Edit, Trash2 } from "@lucide/svelte"
	import * as Tooltip from "$lib/components/ui/tooltip"
	import * as Dialog from "$lib/components/ui/dialog"
	import * as ContextMenu from "$lib/components/ui/context-menu"

	import { Input } from "$lib/components/ui/input"
	import { Label } from "$lib/components/ui/label"
	import { Button } from "$lib/components/ui/button"

	type ServerType = ComponentProps<typeof Server>["server"]

	type Props = {
		servers: ServerType[]
		onSelect: (serverId: ServerType["id"] | null) => void
		onServerAdded?: (server: ServerType) => void
		onServerDeleted?: (serverId: string) => void
		onServerUpdated?: (server: ServerType) => void
		currentUserId?: string
	}

	const {
		servers,
		onSelect,
		onServerAdded,
		onServerDeleted,
		onServerUpdated,
		currentUserId,
	}: Props = $props()
	let selected: ServerType["id"] | null = $state(null)

	// Form states
	let createServerName = $state("Meu servidor")
	let createServerDescription = $state("Descrição")
	let joinServerId = $state("")
	let isCreating = $state(false)
	let isJoining = $state(false)
	let dialogOpen = $state(false)

	// Edit server states
	let editServerName = $state("")
	let editServerDescription = $state("")
	let editingServerId = $state<string | null>(null)
	let editDialogOpen = $state(false)
	let isUpdating = $state(false)
	let isDeleting = $state(false)

	$effect(() => {
		onSelect(selected)
	})

	async function createServer() {
		if (isCreating) return
		isCreating = true

		try {
			const response = await fetch("/api/v1/servers", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					name: createServerName,
					description: createServerDescription,
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Add the new server to the list
			if (onServerAdded) {
				onServerAdded({
					id: result.result.id,
					name: result.result.name,
					description: result.result.description,
					image: "",
					ownerId: currentUserId || "",
					createdAt: new Date(),
				})
			}

			// Reset form and close dialog
			createServerName = "Meu servidor"
			createServerDescription = "Descrição"
			dialogOpen = false
		} catch (error) {
			alert("Falha ao criar servidor")
		} finally {
			isCreating = false
		}
	}

	async function joinServer() {
		if (isJoining) return
		isJoining = true

		try {
			const response = await fetch("/api/v1/servers/join", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					serverId: joinServerId,
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Add the joined server to the list
			if (onServerAdded) {
				onServerAdded({
					id: result.result.id,
					name: result.result.name,
					description: result.result.description,
					image: result.result.image || "",
					ownerId: result.result.ownerId,
					createdAt: new Date(result.result.createdAt),
				})
			}

			// Reset form and close dialog
			joinServerId = ""
			dialogOpen = false
		} catch (error) {
			alert("Falha ao entrar no servidor")
		} finally {
			isJoining = false
		}
	}

	function handleServerContextMenu(serverId: string, event: MouseEvent) {
		event.preventDefault()
		const server = servers.find((s) => s.id === serverId)
		if (!server) return

		// Only show context menu for servers owned by current user
		if (server.ownerId !== currentUserId) return

		editingServerId = serverId
		editServerName = server.name
		editServerDescription = server.description
		editDialogOpen = true
	}

	async function updateServer() {
		if (!editingServerId || isUpdating) return
		isUpdating = true

		try {
			const response = await fetch(`/api/v1/servers/${editingServerId}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					name: editServerName,
					description: editServerDescription,
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Update the server in the list
			if (onServerUpdated) {
				onServerUpdated({
					...servers.find((s) => s.id === editingServerId)!,
					name: editServerName,
					description: editServerDescription,
				})
			}

			// Close dialog
			editDialogOpen = false
			editingServerId = null
		} catch (error) {
			alert("Falha ao atualizar servidor")
		} finally {
			isUpdating = false
		}
	}

	async function deleteServer() {
		if (!editingServerId || isDeleting) return

		if (
			!confirm("Tem a certeza que quer eliminar este servidor? Esta ação não pode ser desfeita.")
		) {
			return
		}

		isDeleting = true

		try {
			const response = await fetch(`/api/v1/servers/${editingServerId}`, {
				method: "DELETE",
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Remove server from list
			if (onServerDeleted) {
				onServerDeleted(editingServerId)
			}

			// Close dialog
			editDialogOpen = false
			editingServerId = null
		} catch (error) {
			alert("Falha ao eliminar servidor")
		} finally {
			isDeleting = false
		}
	}
</script>

<div class="mr-4 ml-4 flex flex-col gap-2">
	{#each servers as server}
		<div
			onclick={() => (selected = server.id)}
			oncontextmenu={(e) => handleServerContextMenu(server.id, e)}
			role="none"
		>
			<Server {server} selected={selected === server.id} />
		</div>
	{/each}

	<Dialog.Root bind:open={dialogOpen}>
		<Dialog.Trigger class="bg-secondary rounded-full p-2">
			<Plus />
		</Dialog.Trigger>
		<Dialog.Content class="max-w-md">
			<div class="space-y-6">
				<!-- Create Server Section -->
				<div>
					<Dialog.Title>Criar um servidor</Dialog.Title>
					<div class="grid gap-4 py-4">
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="create-name" class="text-right">Nome</Label>
							<Input
								id="create-name"
								bind:value={createServerName}
								class="col-span-3"
								placeholder="Nome do servidor"
							/>
						</div>
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="create-description" class="text-right">Descrição</Label>
							<Input
								id="create-description"
								bind:value={createServerDescription}
								class="col-span-3"
								placeholder="Descrição do servidor"
							/>
						</div>
					</div>
					<div class="flex justify-end">
						<Button onclick={createServer} disabled={isCreating}>
							{isCreating ? "Criando..." : "Criar"}
						</Button>
					</div>
				</div>

				<!-- Divider -->
				<div class="border-border border-t"></div>

				<!-- Join Server Section -->
				<div>
					<Dialog.Title>Entrar num servidor</Dialog.Title>
					<div class="grid gap-4 py-4">
						<div class="grid grid-cols-4 items-center gap-4">
							<Label for="join-id" class="text-right">ID</Label>
							<Input
								id="join-id"
								bind:value={joinServerId}
								class="col-span-3"
								placeholder="ID do servidor"
							/>
						</div>
					</div>
					<div class="flex justify-end">
						<Button onclick={joinServer} disabled={isJoining || !joinServerId.trim()}>
							{isJoining ? "Entrando..." : "Entrar"}
						</Button>
					</div>
				</div>
			</div>
		</Dialog.Content>
	</Dialog.Root>

	<!-- Edit Server Dialog -->
	<Dialog.Root bind:open={editDialogOpen}>
		<Dialog.Content class="max-w-md">
			<Dialog.Title>Editar Servidor</Dialog.Title>
			<div class="grid gap-4 py-4">
				<div class="grid grid-cols-4 items-center gap-4">
					<Label for="edit-server-name" class="text-right">Nome</Label>
					<Input
						id="edit-server-name"
						bind:value={editServerName}
						class="col-span-3"
						placeholder="Nome do servidor"
					/>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label for="edit-server-description" class="text-right">Descrição</Label>
					<Input
						id="edit-server-description"
						bind:value={editServerDescription}
						class="col-span-3"
						placeholder="Descrição do servidor"
					/>
				</div>
			</div>
			<Dialog.Footer class="flex justify-between">
				<Button variant="destructive" onclick={deleteServer} disabled={isDeleting || isUpdating}>
					<Trash2 class="mr-2 h-4 w-4" />
					{isDeleting ? "A eliminar..." : "Eliminar"}
				</Button>
				<div class="flex gap-2">
					<Button variant="outline" onclick={() => (editDialogOpen = false)}>Cancelar</Button>
					<Button
						onclick={updateServer}
						disabled={isUpdating || isDeleting || !editServerName.trim()}
					>
						<Edit class="mr-2 h-4 w-4" />
						{isUpdating ? "A guardar..." : "Guardar"}
					</Button>
				</div>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
</div>
