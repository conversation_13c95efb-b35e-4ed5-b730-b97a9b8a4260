import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq, and } from "drizzle-orm"
import { error, success } from "$lib/utils"
import * as z from "zod"

export const POST: RequestHandler = async ({ request, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const bodySchema = z.object({
		serverId: z.string().min(1),
	})

	let json
	try {
		json = await request.json()
	} catch (e) {
		return error(400, "Invalid JSON")
	}

	const body = bodySchema.safeParse(json)
	if (!body.success) return error(400, body.error.message)

	// Check if server exists
	const server = await db.query.servers.findFirst({
		where: eq(schema.servers.id, body.data.serverId),
	})

	if (!server) return error(404, "Server not found")

	// Check if user is already in the server
	const existingMembership = await db.query.userInServer.findFirst({
		where: and(
			eq(schema.userInServer.userId, locals.user.id),
			eq(schema.userInServer.serverId, body.data.serverId)
		),
	})

	if (existingMembership) return error(400, "Already a member of this server")

	try {
		// Add user to server
		await db.insert(schema.userInServer).values({
			userId: locals.user.id,
			serverId: body.data.serverId,
		})

		return success({
			id: server.id,
			name: server.name,
			description: server.description,
			image: server.image,
			ownerId: server.ownerId,
			createdAt: server.createdAt,
		})
	} catch (e) {
		console.error("Error joining server:", e)
		return error(500, "Failed to join server")
	}
}
