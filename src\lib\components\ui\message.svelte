<script lang="ts">
	import * as Avatar from "$lib/components/ui/avatar/index.js"
	import RenderDate from "$lib/components/ui/renderDate.svelte"

	const {
		message,
	}: {
		message: {
			author: { id: string; pfp?: string; username: string }
			content: string
			date: Date
		}
	} = $props()
	const { author, content, date } = message
</script>

<div class="m-1 rounded hover:bg-white/5">
	<div class="flex flex-row gap-1">
		<Avatar.Root class="mt-0.5">
			<Avatar.Image src={author.pfp} alt="what" />
			<Avatar.Fallback>{author.username.at(0)?.toUpperCase()}</Avatar.Fallback>
		</Avatar.Root>
		<div>
			<div class="flex flex-row items-baseline gap-1">
				<div class="hover:underline">{author.username}</div>
				<RenderDate class="text-xs text-gray-400" {date} />
			</div>
			<div class="break-all whitespace-pre-line">{content}</div>
		</div>
	</div>
</div>
