import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import { error, success } from "$lib/utils"

export const DELETE: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const message = await db.query.messages.findFirst({
		where: eq(schema.messages.id, params.id),
	})

	if (!message) return error(404, "Message not found")
	if (message.authorId !== locals.user.id) return error(403, "Can only delete your own messages")

	await db.delete(schema.messages).where(eq(schema.messages.id, params.id))

	return success()
}
