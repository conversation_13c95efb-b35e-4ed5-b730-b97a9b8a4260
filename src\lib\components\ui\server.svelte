<script lang="ts">
	import * as Icon from "$lib/components/ui/icon/index.js"
	import * as Tooltip from "$lib/components/ui/tooltip/index.js"
	import { cn } from "$lib/utils"
	import RenderDate from "$lib/components/ui/renderDate.svelte"

	const {
		server,
		selected,
	}: {
		server: {
			id: string
			name: string
			description: string
			image: string
			createdAt: Date
		}
		selected: boolean
	} = $props()
	const { name, description, image, createdAt } = server
</script>

<Tooltip.Provider delayDuration={0}>
	<Tooltip.Root>
		<Tooltip.Trigger>
			<div class="group relative flex items-center">
				<div
					class={cn(
						"bg-foreground fixed left-0 h-0 w-0 rounded-r transition-[width,height] group-hover:h-5 group-hover:w-1",
						selected && "h-10 w-1 group-hover:h-10 group-hover:w-1"
					)}
				></div>
				<Icon.Root>
					<Icon.Image src={image} alt="what" />
					<Icon.Fallback>{name.at(0)?.toUpperCase()}</Icon.Fallback>
				</Icon.Root>
			</div>
		</Tooltip.Trigger>
		<Tooltip.Content side="right">
			<div class="text-lg font-bold">{name}</div>
			<div>{description}</div>
			<div>Criado em <RenderDate date={createdAt} /></div>
			<div>ID: {server.id}</div>
		</Tooltip.Content>
	</Tooltip.Root>
</Tooltip.Provider>
