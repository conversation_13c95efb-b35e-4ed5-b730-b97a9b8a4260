<script lang="ts">
	import { MessageCircle } from "@lucide/svelte"

	type Props = {
		children?: import("svelte").Snippet
	}

	const { children }: Props = $props()
</script>

<header class="bg-secondary/50 border-b backdrop-blur-sm">
	<div class="flex items-center justify-between px-4 py-4">
		<div class="flex items-center space-x-2">
			<MessageCircle />
			<h1 class="text-2xl font-bold">Wyvern</h1>
		</div>
		{#if children}
			<div class="flex items-center space-x-4">
				{@render children()}
			</div>
		{/if}
	</div>
</header>
