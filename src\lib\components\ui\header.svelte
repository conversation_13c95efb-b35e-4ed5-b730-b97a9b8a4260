<script lang="ts">
	import { MessageCircle } from "@lucide/svelte"
	import { Button } from "$lib/components/ui/button"
	import { enhance } from "$app/forms"

	type Props = {
		children?: import("svelte").Snippet
		user?: { id: string; username: string } | null
		showAuth?: boolean
	}

	const { children, user, showAuth = true }: Props = $props()
</script>

<header class="bg-secondary/50 border-b backdrop-blur-sm">
	<div class="flex items-center justify-between px-4 py-4">
		<div class="flex items-center space-x-2">
			<MessageCircle />
			<h1 class="text-2xl font-bold">Wyvern</h1>
		</div>
		<div class="flex items-center space-x-4">
			{#if children}
				{@render children()}
			{/if}

			{#if showAuth}
				{#if user}
					<span class="text-muted-foreground text-sm">O<PERSON><PERSON> {user.username}!</span>
					<form method="post" action="/dashboard?/logout" use:enhance>
						<Button variant="outline" size="sm">
							<button><PERSON><PERSON><PERSON><PERSON></button>
						</Button>
					</form>
				{:else}
					<Button variant="ghost" href="/login">Iniciar Sessão</Button>
					<Button href="/dashboard">Entrar</Button>
				{/if}
			{/if}
		</div>
	</div>
</header>
