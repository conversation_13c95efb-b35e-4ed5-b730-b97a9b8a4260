import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import { error, success } from "$lib/utils"
import * as z from "zod"

export const GET: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const servers = await db
		.select({
			id: schema.servers.id,
			name: schema.servers.name,
			description: schema.servers.description,
			image: schema.servers.image,
			ownerId: schema.servers.ownerId,
			createdAt: schema.servers.createdAt,
		})
		.from(schema.servers)
		.innerJoin(schema.userInServer, eq(schema.servers.id, schema.userInServer.serverId))
		.where(eq(schema.userInServer.userId, locals.user.id))

	return success(servers)
}

export const PATCH: RequestHandler = async ({ params, request, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const bodySchema = z.object({
		name: z.string().min(1).max(100),
		description: z.string().min(1).max(1000),
	})

	let json
	try {
		json = await request.json()
	} catch (e) {
		return error(400, "Invalid JSON")
	}

	const body = bodySchema.safeParse(json)
	if (!body.success) return error(400, body.error.message)

	// Check if server exists and user is owner
	const server = await db.query.servers.findFirst({
		where: eq(schema.servers.id, params.id),
	})

	if (!server) return error(404, "Server not found")
	if (server.ownerId !== locals.user.id) return error(403, "Only server owner can update server")

	try {
		await db
			.update(schema.servers)
			.set({
				name: body.data.name,
				description: body.data.description,
			})
			.where(eq(schema.servers.id, params.id))

		return success({ id: params.id, name: body.data.name, description: body.data.description })
	} catch (e) {
		return error(500, "Failed to update server")
	}
}

export const DELETE: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	// Check if server exists and user is owner
	const server = await db.query.servers.findFirst({
		where: eq(schema.servers.id, params.id),
	})

	if (!server) return error(404, "Server not found")
	if (server.ownerId !== locals.user.id) return error(403, "Only server owner can delete server")

	try {
		// Delete server (this will cascade delete channels, messages, etc.)
		await db.delete(schema.servers).where(eq(schema.servers.id, params.id))

		return success({ message: "Server deleted successfully" })
	} catch (e) {
		return error(500, "Failed to delete server")
	}
}
