import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import { error, success } from "$lib/utils"

export const GET: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const servers = await db
		.select({
			id: schema.servers.id,
			name: schema.servers.name,
			description: schema.servers.description,
			image: schema.servers.image,
			ownerId: schema.servers.ownerId,
			createdAt: schema.servers.createdAt,
		})
		.from(schema.servers)
		.innerJoin(schema.userInServer, eq(schema.servers.id, schema.userInServer.serverId))
		.where(eq(schema.userInServer.userId, locals.user.id))

	return success(servers)
}
