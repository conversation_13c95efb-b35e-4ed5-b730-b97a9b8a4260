<script lang="ts">
	import type { PageServerData } from "./$types"
	import { enhance } from "$app/forms"
	import { Button } from "$lib/components/ui/button"
	import Messages from "$lib/components/ui/messages.svelte"
	import Servers from "$lib/components/ui/servers.svelte"
	import { onMount, type ComponentProps } from "svelte"
	import Channels from "$lib/components/ui/channels.svelte"
	import Header from "$lib/components/ui/header.svelte"

	let { data }: { data: PageServerData } = $props()

	let serverId: string | null = $state(null)
	let servers: ComponentProps<typeof Servers>["servers"] = $state([])
	onMount(async () => {
		const res = await fetch("/api/v1/servers", { method: "GET" })
		const json = await res.json()
		if (!json.success) {
			alert(json.error)
			return
		}
		servers = json.result
		servers = servers.map((server) => {
			return { ...server, createdAt: new Date(server.createdAt) }
		})
	})

	let channelId: string | null = $state(null)
	let channels: ComponentProps<typeof Channels>["channels"] = $state([])
	$effect(() => {
		;(async () => {
			if (!serverId) {
				channels = []
				return
			}
			const res = await fetch(`/api/v1/servers/${serverId}/channels`, { method: "GET" })
			const json = await res.json()
			if (!json.success) {
				alert(json.error)
				return
			}
			channels = json.result
		})()
	})

	let messages: ComponentProps<typeof Messages>["messages"] = $state([])
	$effect(() => {
		;(async () => {
			if (!channelId) {
				messages = []
				return
			}
			const res = await fetch(`/api/v1/channels/${channelId}/messages`, { method: "GET" })
			const json = await res.json()
			if (!json.success) {
				alert(json.error)
				return
			}
			messages = json.result
			messages = messages.map((message) => {
				console.log(message.date, typeof message.date)
				return { ...message, date: new Date(message.date) }
			})
		})()
	})
</script>

<svelte:head>
	<title>Wyvern - Dashboard</title>
	<!-- TODO: change based on channel and server -->
</svelte:head>

<Header>
	<span class="text-muted-foreground text-sm">Olá {data.user.username}!</span>
	<Button variant="outline" size="sm">
		<form method="post" action="?/logout" use:enhance>
			<button>Terminar Sessão</button>
		</form>
	</Button>
</Header>

<div class="mt-4 flex">
	<Servers
		{servers}
		onSelect={(id) => {
			serverId = id
		}}
		onServerAdded={(server) => {
			servers = [...servers, server]
		}}
	/>

	{#if serverId}
		<Channels
			{channels}
			{serverId}
			onSelect={(id) => {
				channelId = id
			}}
			onChannelAdded={(channel) => {
				channels = [...channels, channel]
			}}
		/>
	{/if}

	{#if channelId}
		<Messages {messages} />
	{/if}
</div>