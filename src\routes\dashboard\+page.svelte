<script lang="ts">
	import type { PageServerData } from "./$types"
	import { enhance } from "$app/forms"
	import { Button } from "$lib/components/ui/button"
	import Messages from "$lib/components/ui/messages.svelte"
	import Servers from "$lib/components/ui/servers.svelte"
	import { onMount, type ComponentProps } from "svelte"
	import Channels from "$lib/components/ui/channels.svelte"

	let { data }: { data: PageServerData } = $props()

	let serverId: string | null = $state(null)
	let servers: ComponentProps<typeof Servers>["servers"] = $state([])
	onMount(async () => {
		const res = await fetch("/api/v1/servers", { method: "GET" })
		const json = await res.json()
		if (!json.success) {
			alert(json.error)
			return
		}
		servers = json.result
		servers = servers.map((server) => {
			return { ...server, createdAt: new Date(server.createdAt) }
		})
	})

	let channelId: string | null = $state(null)
	let channels: ComponentProps<typeof Channels>["channels"] = $state([])
	$effect(() => {
		;(async () => {
			if (!serverId) {
				channels = []
				return
			}
			const res = await fetch(`/api/v1/servers/${serverId}/channels`, { method: "GET" })
			const json = await res.json()
			if (!json.success) {
				alert(json.error)
				return
			}
			channels = json.result
		})()
	})

	let messages: ComponentProps<typeof Messages>["messages"] = $state([])
	$effect(() => {
		;(async () => {
			if (!channelId) {
				messages = []
				return
			}
			const res = await fetch(`/api/v1/channels/${channelId}/messages`, { method: "GET" })
			const json = await res.json()
			if (!json.success) {
				alert(json.error)
				return
			}
			messages = json.result
			messages = messages.map((message) => {
				console.log(message.date, typeof message.date)
				return { ...message, date: new Date(message.date) }
			})
		})()
	})
</script>

<div class="bg-secondary grid grid-cols-3 items-center rounded-b-md pr-4 pl-4">
	<div class="">Hi {data.user.username}!</div>
	<p class="mb-4 text-center text-5xl">Wyvern</p>
	<div class="flex flex-row-reverse">
		<Button>
			<form method="post" action="?/logout" use:enhance>
				<button>Logout</button>
			</form>
		</Button>
	</div>
</div>

<div class="mt-4 flex">
	<Servers
		{servers}
		onSelect={(id) => {
			serverId = id
		}}
		onServerAdded={(server) => {
			servers = [...servers, server]
		}}
	/>

	<Channels
		{channels}
		onSelect={(id) => {
			channelId = id
		}}
	/>

	<Messages {messages} />
</div>

<!-- <Message
	message={{
		user: {
			id: "1",
			pfp: "https://cdn.discordapp.com/avatars/570278979655827471/a_bab5e9a075340ddc593a7286c1ec0594.webp",
			username: "Zombinho",
		},

		content: test,
		date: new Date(),
	}}
/>
 -->
<!-- <ServerPicker {servers} onSelect={(s) => (server = s)} /> -->
<!-- <ChannelPicker channels={server.channels} onSelect={() => {}} /> -->
