<script lang="ts">
	import type { PageServerData } from "./$types"
	import Messages from "$lib/components/ui/messages.svelte"
	import Servers from "$lib/components/ui/servers.svelte"
	import { onDestroy, onMount, type ComponentProps } from "svelte"
	import Channels from "$lib/components/ui/channels.svelte"
	import Header from "$lib/components/ui/header.svelte"
	import ChatBox from "$lib/components/ui/chatbox.svelte"

	let time = $state(0)
	const timeInterval = setInterval(() => {
		time += 1
	}, 1000)
	onDestroy(() => {
		clearInterval(timeInterval)
	})
	let { data }: { data: PageServerData } = $props()

	let serverId: string | null = $state(null)
	let servers: ComponentProps<typeof Servers>["servers"] = $state([])
	$effect(() => {
		;(async () => {
			console.log(time)
			const res = await fetch("/api/v1/servers", { method: "GET" })
			const json = await res.json()
			if (!json.success) {
				alert(json.error)
				return
			}
			servers = json.result
			servers = servers.map((server) => {
				return { ...server, createdAt: new Date(server.createdAt) }
			})
		})()
	})

	let channelId: string | null = $state(null)
	let channels: ComponentProps<typeof Channels>["channels"] = $state([])
	$effect(() => {
		;(async () => {
			console.log(time)
			if (!serverId) {
				channels = []
				return
			}
			const res = await fetch(`/api/v1/servers/${serverId}/channels`, { method: "GET" })
			const json = await res.json()
			if (!json.success) {
				alert(json.error)
				return
			}
			channels = json.result
		})()
	})

	let messages: ComponentProps<typeof Messages>["messages"] = $state([])
	$effect(() => {
		;(async () => {
			console.log(time)
			if (!channelId) {
				messages = []
				return
			}
			const res = await fetch(`/api/v1/channels/${channelId}/messages`, { method: "GET" })
			const json = await res.json()
			if (!json.success) {
				alert(json.error)
				return
			}
			messages = json.result
			messages = messages.map((message: any) => {
				return { ...message, date: new Date(message.createdAt) }
			})
		})()
	})

	function handleMessageSent(newMessage: any) {
		// Add the new message directly to the messages list
		const messageWithDate = {
			...newMessage,
			date: new Date(newMessage.createdAt),
		}
		messages = [...messages, messageWithDate]
	}
</script>

<svelte:head>
	<title>Wyvern - Dashboard</title>
	<!-- TODO: change based on channel and server -->
</svelte:head>

<Header user={data.user} />

<div class="mt-4 flex h-[calc(100vh-8rem)]">
	<Servers
		{servers}
		currentUserId={data.user.id}
		onSelect={(id) => {
			serverId = id
			channelId = null // Reset channel when switching servers
		}}
		onServerAdded={(server) => {
			servers = [...servers, server]
		}}
		onServerDeleted={(deletedServerId) => {
			servers = servers.filter((s) => s.id !== deletedServerId)
			if (serverId === deletedServerId) {
				serverId = null
				channelId = null
				channels = []
				messages = []
			}
		}}
		onServerUpdated={(updatedServer) => {
			servers = servers.map((s) => (s.id === updatedServer.id ? updatedServer : s))
		}}
	/>

	{#if serverId}
		<Channels
			{channels}
			{serverId}
			currentUserId={data.user.id}
			onSelect={(id) => {
				channelId = id
			}}
			onChannelAdded={(channel) => {
				channels = [...channels, channel]
			}}
			onChannelDeleted={(deletedChannelId) => {
				channels = channels.filter((c) => c.id !== deletedChannelId)
				if (channelId === deletedChannelId) {
					channelId = null
					messages = []
				}
			}}
			onChannelUpdated={(updatedChannel) => {
				channels = channels.map((c) => (c.id === updatedChannel.id ? updatedChannel : c))
			}}
		/>
	{/if}

	{#if channelId}
		<div class="flex min-h-0 flex-1 flex-col">
			<div class="flex-1 overflow-y-auto">
				<Messages {messages} />
			</div>
			<ChatBox {channelId} onMessageSent={handleMessageSent} />
		</div>
	{/if}
</div>
