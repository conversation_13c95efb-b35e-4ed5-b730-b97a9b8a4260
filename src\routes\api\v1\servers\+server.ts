import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import { error, success } from "$lib/utils"
import * as z from "zod"
import { encodeBase32LowerCase } from "@oslojs/encoding"

export const GET: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) return error(401, "Não autorizado")

	const servers = await db
		.select({
			id: schema.servers.id,
			name: schema.servers.name,
			description: schema.servers.description,
			image: schema.servers.image,
			ownerId: schema.servers.ownerId,
			createdAt: schema.servers.createdAt,
		})
		.from(schema.servers)
		.innerJoin(schema.userInServer, eq(schema.servers.id, schema.userInServer.serverId))
		.where(eq(schema.userInServer.userId, locals.user.id))

	return success(servers)
}

export const POST: RequestHandler = async ({ request, locals }) => {
	if (!locals.user) return error(401, "Não autorizado")

	const bodySchema = z.object({
		name: z.string().min(1).max(100),
		description: z.string().min(1).max(1000),
	})

	let json
	try {
		json = await request.json()
	} catch (e) {
		return error(400, "JSON inválido")
	}

	const body = bodySchema.safeParse(json)
	if (!body.success) return error(400, body.error.message)

	// Generate server ID
	const bytes = crypto.getRandomValues(new Uint8Array(15))
	const serverId = encodeBase32LowerCase(bytes)

	try {
		// Create the server
		await db.insert(schema.servers).values({
			id: serverId,
			name: body.data.name,
			description: body.data.description,
			ownerId: locals.user.id,
			createdAt: new Date(),
		})

		// Add the creator to the server
		await db.insert(schema.userInServer).values({
			userId: locals.user.id,
			serverId: serverId,
		})

		// Create a default "general" channel
		const channelBytes = crypto.getRandomValues(new Uint8Array(15))
		const channelId = encodeBase32LowerCase(channelBytes)

		await db.insert(schema.channels).values({
			id: channelId,
			serverId: serverId,
			name: "general",
			description: "General discussion",
			createdAt: new Date(),
		})

		return success({ id: serverId, name: body.data.name, description: body.data.description })
	} catch (e) {
		return error(500, "Failed to create server")
	}
}
