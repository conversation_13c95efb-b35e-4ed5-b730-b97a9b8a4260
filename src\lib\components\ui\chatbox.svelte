<script lang="ts">
	import { But<PERSON> } from "$lib/components/ui/button"
	import { Input } from "$lib/components/ui/input"
	import { Send } from "@lucide/svelte"

	type Props = {
		channelId: string | null
		onMessageSent?: (message: { content: string }) => void
	}

	const { channelId, onMessageSent }: Props = $props()
	
	let messageContent = $state("")
	let isSending = $state(false)

	async function sendMessage() {
		if (!messageContent.trim() || !channelId || isSending) return
		
		isSending = true
		
		try {
			const response = await fetch(`/api/v1/channels/${channelId}/messages`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					content: messageContent.trim(),
				}),
			})

			const result = await response.json()
			if (!result.success) {
				alert(result.error)
				return
			}

			// Notify parent component that a message was sent
			if (onMessageSent) {
				onMessageSent({
					content: messageContent.trim(),
				})
			}

			// Clear the input
			messageContent = ""
		} catch (error) {
			alert("Falha ao enviar mensagem")
		} finally {
			isSending = false
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === "Enter" && !event.shiftKey) {
			event.preventDefault()
			sendMessage()
		}
	}
</script>

{#if channelId}
	<div class="border-t bg-background p-4">
		<div class="flex items-center space-x-2">
			<Input
				bind:value={messageContent}
				placeholder="Escreva uma mensagem..."
				class="flex-1"
				disabled={isSending}
				onkeypress={handleKeyPress}
			/>
			<Button 
				onclick={sendMessage} 
				disabled={!messageContent.trim() || isSending}
				size="sm"
			>
				{#if isSending}
					<div class="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
				{:else}
					<Send class="h-4 w-4" />
				{/if}
			</Button>
		</div>
	</div>
{/if}
