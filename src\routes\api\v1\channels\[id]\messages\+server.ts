import { error } from "@sveltejs/kit"
import type { Request<PERSON>and<PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import * as z from "zod"
import { getAuthorizedChannel } from "$lib/server/auth"
import { success } from "$lib/utils"

export const GET: RequestHandler = async ({ params, url, locals }) => {
	if (!locals.user) error(401, "Unauthorized")

	const limit = z.coerce
		.number()
		.min(1)
		.max(100)
		.default(100)
		.safeParse(url.searchParams.get("limit") ?? undefined)
	if (!limit.success) return error(400, limit.error.message)

	const offset = z.coerce
		.number()
		.min(0)
		.default(0)
		.safeParse(url.searchParams.get("offset") ?? undefined)
	if (!offset.success) return error(400, "Invalid offset")

	const channel = await getAuthorizedChannel(params.id, locals.user.id)
	const messages = await db.query.messages.findMany({
		where: eq(schema.messages.channelId, channel.id),
		limit: limit.data,
		offset: offset.data,
		with: {
			author: true,
		},
	})

	return success(messages)
}
