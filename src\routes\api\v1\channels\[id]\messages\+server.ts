import { error } from "@sveltejs/kit"
import type { RequestHand<PERSON> } from "./$types"
import { db, schema } from "$lib/server/db"
import { eq } from "drizzle-orm"
import * as z from "zod"
import { getAuthorizedChannel } from "$lib/server/auth"
import { success } from "$lib/utils"
import { encodeBase32LowerCase } from "@oslojs/encoding"

export const GET: RequestHandler = async ({ params, url, locals }) => {
	if (!locals.user) error(401, "Unauthorized")

	const limit = z.coerce
		.number()
		.min(1)
		.max(100)
		.default(100)
		.safeParse(url.searchParams.get("limit") ?? undefined)
	if (!limit.success) return error(400, limit.error.message)

	const offset = z.coerce
		.number()
		.min(0)
		.default(0)
		.safeParse(url.searchParams.get("offset") ?? undefined)
	if (!offset.success) return error(400, "Invalid offset")

	const channel = await getAuthorizedChannel(params.id, locals.user.id)
	const messages = await db.query.messages.findMany({
		where: eq(schema.messages.channelId, channel.id),
		limit: limit.data,
		offset: offset.data,
		with: {
			author: true,
		},
	})

	return success(messages)
}

export const POST: RequestHandler = async ({ params, request, locals }) => {
	if (!locals.user) return error(401, "Unauthorized")

	const bodySchema = z.object({
		content: z.string().min(1).max(2000),
	})

	let json
	try {
		json = await request.json()
	} catch (e) {
		return error(400, "Invalid JSON")
	}

	const body = bodySchema.safeParse(json)
	if (!body.success) return error(400, body.error.message)

	const channel = await getAuthorizedChannel(params.id, locals.user.id)

	// Generate message ID
	const bytes = crypto.getRandomValues(new Uint8Array(15))
	const messageId = encodeBase32LowerCase(bytes)

	try {
		// Create the message
		const createdAt = new Date()
		await db.insert(schema.messages).values({
			id: messageId,
			authorId: locals.user.id,
			channelId: channel.id,
			content: body.data.content,
			createdAt: createdAt,
		})

		// Get the user info for the response
		const user = await db.query.users.findFirst({
			where: eq(schema.users.id, locals.user.id),
		})

		return success({
			id: messageId,
			content: body.data.content,
			authorId: locals.user.id,
			channelId: channel.id,
			createdAt: createdAt,
			author: {
				id: user?.id || locals.user.id,
				username: user?.username || "Unknown",
				pfp: user?.pfp || null,
			},
		})
	} catch (e) {
		return error(500, "Failed to send message")
	}
}
