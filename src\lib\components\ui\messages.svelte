<script lang="ts">
	import * as Avatar from "$lib/components/ui/avatar/index.js"
	import RenderDate from "$lib/components/ui/renderDate.svelte"
	import type { ComponentProps } from "svelte"
	import Message from "$lib/components/ui/message.svelte"

	const {
		messages,
	}: {
		messages: ComponentProps<typeof Message>["message"][]
	} = $props()
</script>

<div>
	{#each messages as message}
		<Message {message} />
	{/each}
</div>
